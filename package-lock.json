{"name": "checkweather-sg-api", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "checkweather-sg-api", "version": "0.1.0", "dependencies": {"nearest-color": "^0.4.4", "pngjs": "^7.0.0"}}, "node_modules/nearest-color": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/nearest-color/-/nearest-color-0.4.4.tgz", "integrity": "sha512-orhcaIORC10tf41Ld2wwlcC+FaAavHG87JHWB3eHH5p7v2k9Tzym2XNEZzLAm5YJwGv6Q38WWc7SOb+Qfu/4NQ==", "license": "MIT"}, "node_modules/pngjs": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/pngjs/-/pngjs-7.0.0.tgz", "integrity": "sha512-LKWqWJRhstyYo9pGvgor/ivk2w94eSjE3RGVuzLGlr3NmD8bf7RcYGze1mNdEHRP6TRP6rMuDHk5t44hnTRyow==", "license": "MIT", "engines": {"node": ">=14.19.0"}}}}